/**
 * MissaResponse«SearchResult«DhxyCodeInfoListVO»»
 */
export interface ApifoxModel {
    body?: SearchResultDhxyCodeInfoListVO;
    headers?: HttpHeaders;
    httpStatus?: number | null;
    inputStream?: { [key: string]: any };
    missaResponseEntity?: MissaResponseEntitySearchResultDhxyCodeInfoListVO;
    [property: string]: any;
}

/**
 * SearchResult«DhxyCodeInfoListVO»
 *
 * 响应数据,object格式，非必须
 * <p>
 * 自定义的响应内容结构
 * </p>
 */
export interface SearchResultDhxyCodeInfoListVO {
    /**
     * 分页信息
     */
    pagination?: Pagination;
    /**
     * 结果列表
     */
    result?: DhxyCodeInfoListVO[] | null;
    [property: string]: any;
}

/**
 * 分页信息
 *
 * Pagination
 */
export interface Pagination {
    /**
     * 当前页
     */
    page?: number | null;
    /**
     * 页大小
     */
    size?: number | null;
    /**
     * 结果总数
     */
    total?: number | null;
    /**
     * 总页数
     */
    totalPage?: number | null;
    [property: string]: any;
}

/**
 * com.netease.yx.act.admin.vo.dhxy.DhxyCodeInfoListVO
 *
 * DhxyCodeInfoListVO
 */
export interface DhxyCodeInfoListVO {
    /**
     * 激活码总数
     */
    actCodeSum?: number | null;
    /**
     * 活动编号(如年份)
     */
    actNumber?: null | string;
    /**
     * 激活码类型<br/>
     * 1：预约码(预沟序列号)<br/>
     * 2：激活码(激活序列号)
     */
    codeType?: number;
    /**
     * 创建时间
     */
    createTime?: number | null;
    /**
     * 版本<br/>
     * 1：经典版<br/>
     * 2：免费版
     */
    edition?: number;
    /**
     * 主键，新建时不传，更新时必传
     */
    id?: number | null;
    /**
     * 物品类型<br/>
     * 1：分享版<br/>
     * 2：真爱版<br/>
     * 3：白金版<br/>
     * 4：典藏版
     */
    itemType?: number;
    /**
     * 预约码未使用数
     */
    resCodeNotUsedSum?: number | null;
    /**
     * 预约码总数
     */
    resCodeSum?: number | null;
    /**
     * 预约码已使用数
     */
    resCodeUsedSum?: number | null;
    [property: string]: any;
}

/**
 * HttpHeaders
 */
export interface HttpHeaders {
    key?: string[];
    [property: string]: any;
}

/**
 * MissaResponseEntity«SearchResult«DhxyCodeInfoListVO»»
 */
export interface MissaResponseEntitySearchResultDhxyCodeInfoListVO {
    /**
     * 子响应码，number格式,非必须
     * <p>
     * 一般在业务异常或有业务特殊含义时使用
     * </p>
     */
    code?: number | null;
    /**
     * 响应数据,object格式，非必须
     * <p>
     * 自定义的响应内容结构
     * </p>
     */
    data?: SearchResultDhxyCodeInfoListVO;
    /**
     * 内部错误详细信息，MissaResponseError格式，非必须
     * <p>
     * 可供调用方快速定位问题，非必须，嵌套格式可追溯到源
     * </p>
     */
    error?: MissaResponseErrorEntity;
    /**
     * Msg消息，字符串格式，非必须
     * <p>
     * 可供调用方直接对终端用户输出和使用的业务报错信息 业务异常必须，其它非必须，不允许在此存放堆栈信息等无意义信息
     * </p>
     */
    message?: null | string;
    [property: string]: any;
}

/**
 * 内部错误详细信息，MissaResponseError格式，非必须
 * <p>
 * 可供调用方快速定位问题，非必须，嵌套格式可追溯到源
 * </p>
 *
 * MissaResponseErrorEntity
 *
 * 嵌套的error结构，非必须
 */
export interface MissaResponseErrorEntity {
    /**
     * 错误的详细说明，非必须
     */
    description?: string;
    /**
     * 嵌套的error结构，非必须
     */
    error?: MissaResponseErrorEntity;
    /**
     * 更加详细的错误异常码，非必须
     */
    errorCode?: number;
    [property: string]: any;
}

/**
 * 查询参数接口
 */
export interface SerialNumberQueryParams {
    /**
     * 创建时间结束
     */
    createTimeEnd?: string;
    /**
     * 创建时间开始
     */
    createTimeStart?: string;
    /**
     * 版本列表
     */
    editions?: number[];
    /**
     * 物品类型列表
     */
    itemTypes?: number[];
    /**
     * 当前页
     */
    page?: number;
    /**
     * 页大小
     */
    size?: number;
}

/**
 * 配置查询响应接口
 */
export interface ConfigQueryResponse {
    /**
     * 版本列表
     */
    editionList?: EditionItem[];
    /**
     * 物品列表
     */
    itemList?: ItemTypeItem[];
}

/**
 * 版本项
 */
export interface EditionItem {
    /**
     * 版本ID
     */
    id: number;
    /**
     * 版本名称
     */
    name: string;
}

/**
 * 物品类型项
 */
export interface ItemTypeItem {
    /**
     * 物品类型ID
     */
    id: number;
    /**
     * 物品类型名称
     */
    name: string;
}
