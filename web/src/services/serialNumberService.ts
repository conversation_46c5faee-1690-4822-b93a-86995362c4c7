import { PlainObject, AjaxPaginationResult } from '@shark/core';
import { axiosService } from '@sharkr/request';
import {
  SearchResultDhxyCodeInfoListVO,
  SerialNumberQueryParams,
  ConfigQueryResponse
} from '../interfaces/serialNumberInterface';

/**
 * 查询序列号列表
 * @param params 查询参数
 * @returns 序列号列表响应
 */
export const querySerialNumberList = (
  params: SerialNumberQueryParams
): Promise<AjaxPaginationResult<SearchResultDhxyCodeInfoListVO>> =>
  axiosService.post('/xhr/dhxy/admin/codeInfo/pageQuery.json', params);

/**
 * 查询配置信息（版本和物品类型）
 * @returns 配置信息响应
 */
export const querySkuAndEdition = (): Promise<AjaxPaginationResult<ConfigQueryResponse>> =>
  axiosService.post('/xhr/dhxy/admin/user/skuAndEdition/query.json', {});
