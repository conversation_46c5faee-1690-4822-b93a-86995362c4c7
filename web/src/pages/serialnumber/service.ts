import { axiosService } from '@sharkr/request';
import { 
  SearchResultDhxyCodeInfoListVO, 
  CodeInfoPageQueryParams,
  ConfigData 
} from './interface';

/**
 * 获取激活序列号列表
 */
export const getCodeInfoList = async (
  params: CodeInfoPageQueryParams
): Promise<SearchResultDhxyCodeInfoListVO> => {
  const response = await axiosService.post('/xhr/dhxy/admin/codeInfo/pageQuery.json', params);
  return response.data?.data || { pagination: null, result: null };
};

/**
 * 获取配置信息（版本和物品类型）
 */
export const getConfigData = async (): Promise<ConfigData> => {
  const response = await axiosService.get('/xhr/dhxy/admin/user/skuAndEdition/query.json');
  return response.data?.data || { editionList: [], itemList: [] };
};
