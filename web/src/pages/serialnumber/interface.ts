/**
 * 分页信息
 */
export interface Pagination {
  /**
   * 当前页
   */
  page?: number | null;
  /**
   * 页大小
   */
  size?: number | null;
  /**
   * 结果总数
   */
  total?: number | null;
  /**
   * 总页数
   */
  totalPage?: number | null;
}

/**
 * 激活序列号列表项 - 根据交互稿调整字段
 */
export interface DhxyCodeInfoListVO {
  /**
   * 主键，新建时不传，更新时必传
   */
  id?: number | null;
  /**
   * 年份/活动编号
   */
  actNumber?: string | null;
  /**
   * 版本类型
   * 1：经典版
   * 2：免费版
   */
  edition?: number;
  /**
   * 商品类型
   * 1：分享版
   * 2：真爱版
   * 3：白金版
   * 4：典藏版
   */
  itemType?: number;
  /**
   * 已激活总数
   */
  activatedTotalSum?: number | null;
  /**
   * 已激活数
   */
  activatedSum?: number | null;
  /**
   * 激活总数
   */
  activationTotalSum?: number | null;
  /**
   * 激活用户数
   */
  activatedUserSum?: number | null;
  /**
   * 创建时间
   */
  createTime?: number | null;
}

/**
 * 搜索结果
 */
export interface SearchResultDhxyCodeInfoListVO {
  /**
   * 分页信息
   */
  pagination?: Pagination;
  /**
   * 结果列表
   */
  result?: DhxyCodeInfoListVO[] | null;
}

/**
 * 列表查询参数
 */
export interface CodeInfoPageQueryParams {
  /**
   * 创建时间结束
   */
  createTimeEnd?: string;
  /**
   * 创建时间开始
   */
  createTimeStart?: string;
  /**
   * 版本列表
   */
  editions?: number[];
  /**
   * 物品类型列表
   */
  itemTypes?: number[];
  /**
   * 当前页
   */
  page?: number;
  /**
   * 页大小
   */
  size?: number;
}

/**
 * 配置选项
 */
export interface ConfigOption {
  label: string;
  value: number;
}

/**
 * 配置数据
 */
export interface ConfigData {
  /**
   * 版本列表
   */
  editionList?: ConfigOption[];
  /**
   * 物品类型列表
   */
  itemList?: ConfigOption[];
}
