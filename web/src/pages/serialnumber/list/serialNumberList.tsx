import React, { useState, useEffect } from 'react';
import { Form, Button, Select, DatePicker, Row, Col, Table, message, Space } from 'antd';
import { EditOutlined, EyeOutlined } from '@ant-design/icons';
import { 
  DhxyCodeInfoListVO, 
  CodeInfoPageQueryParams, 
  ConfigData,
  Pagination 
} from '../interface';
import { getCodeInfoList, getConfigData } from '../service';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

export const SerialNumberList: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<DhxyCodeInfoListVO[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    size: 10,
    total: 0,
    totalPage: 0
  });
  const [configData, setConfigData] = useState<ConfigData>({
    editionList: [],
    itemList: []
  });

  // 版本映射
  const editionMap: { [key: number]: string } = {
    1: '经典版',
    2: '免费版'
  };

  // 商品类型映射
  const itemTypeMap: { [key: number]: string } = {
    1: '分享版',
    2: '真爱版',
    3: '白金版',
    4: '典藏版'
  };

  // 表格列定义 - 严格按照交互稿
  const columns = [
    {
      title: '年份',
      dataIndex: 'actNumber',
      key: 'actNumber',
      width: 100,
      render: (text: string) => text || '-'
    },
    {
      title: '版本类型',
      dataIndex: 'edition',
      key: 'edition',
      width: 120,
      render: (edition: number) => editionMap[edition] || '-'
    },
    {
      title: '商品类型',
      dataIndex: 'itemType',
      key: 'itemType',
      width: 120,
      render: (itemType: number) => itemTypeMap[itemType] || '-'
    },
    {
      title: '已激活总数',
      dataIndex: 'activatedTotalSum',
      key: 'activatedTotalSum',
      width: 120,
      render: (num: number) => num?.toLocaleString() || 0
    },
    {
      title: '已激活数',
      dataIndex: 'activatedSum',
      key: 'activatedSum',
      width: 120,
      render: (num: number) => num?.toLocaleString() || 0
    },
    {
      title: '激活总数',
      dataIndex: 'activationTotalSum',
      key: 'activationTotalSum',
      width: 120,
      render: (num: number) => num?.toLocaleString() || 0
    },
    {
      title: '激活用户数',
      dataIndex: 'activatedUserSum',
      key: 'activatedUserSum',
      width: 120,
      render: (num: number) => num?.toLocaleString() || 0
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
      render: (time: number) => time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right' as const,
      render: (_, record: DhxyCodeInfoListVO) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看详情
          </Button>
        </Space>
      )
    }
  ];

  // 获取配置数据
  const fetchConfigData = async () => {
    try {
      const config = await getConfigData();
      setConfigData(config);
    } catch (error) {
      message.error('获取配置数据失败');
      console.error('获取配置数据失败:', error);
    }
  };

  // 获取列表数据
  const fetchData = async (params?: Partial<CodeInfoPageQueryParams>) => {
    setLoading(true);
    try {
      const queryParams: CodeInfoPageQueryParams = {
        page: pagination.page || 1,
        size: pagination.size || 10,
        ...params
      };

      const result = await getCodeInfoList(queryParams);
      setDataSource(result.result || []);
      setPagination(prev => ({
        ...prev,
        ...result.pagination
      }));
    } catch (error) {
      message.error('获取数据失败');
      console.error('获取数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 搜索处理
  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params: Partial<CodeInfoPageQueryParams> = {
      page: 1, // 重置到第一页
      editions: values.editions,
      itemTypes: values.itemTypes
    };

    // 处理时间范围
    if (values.createTimeRange && values.createTimeRange.length === 2) {
      params.createTimeStart = values.createTimeRange[0].format('YYYY-MM-DD HH:mm:ss');
      params.createTimeEnd = values.createTimeRange[1].format('YYYY-MM-DD HH:mm:ss');
    }

    fetchData(params);
  };

  // 分页处理
  const handleTableChange = (page: number, pageSize?: number) => {
    const newPagination = {
      ...pagination,
      page,
      size: pageSize || pagination.size || 10
    };
    setPagination(newPagination);
    
    const values = form.getFieldsValue();
    const params: Partial<CodeInfoPageQueryParams> = {
      page,
      size: pageSize || pagination.size || 10,
      editions: values.editions,
      itemTypes: values.itemTypes
    };

    if (values.createTimeRange && values.createTimeRange.length === 2) {
      params.createTimeStart = values.createTimeRange[0].format('YYYY-MM-DD HH:mm:ss');
      params.createTimeEnd = values.createTimeRange[1].format('YYYY-MM-DD HH:mm:ss');
    }

    fetchData(params);
  };

  // 编辑操作
  const handleEdit = (record: DhxyCodeInfoListVO) => {
    message.info(`编辑记录 ID: ${record.id}`);
    // TODO: 实现编辑功能
  };

  // 查看详情操作
  const handleView = (record: DhxyCodeInfoListVO) => {
    message.info(`查看详情 ID: ${record.id}`);
    // TODO: 实现查看详情功能
  };

  // 初始化
  useEffect(() => {
    fetchConfigData();
    fetchData();
  }, []);

  return (
    <>
      <section className="sharkr-section">
        <div className="sharkr-section-header">
          <span className="sharkr-section-header-title">激活序列号管理</span>
          <span className="sharkr-section-header-sub-title">
            （共{pagination.total || 0}条）
          </span>
        </div>
        <div className="sharkr-section-content">
          <Form
            form={form}
            className="sharkr-form-inline margin-b-base"
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
          >
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="版本" name="editions">
                  <Select
                    placeholder="请选择版本"
                    mode="multiple"
                    allowClear
                    style={{ width: '100%' }}
                  >
                    {configData.editionList?.map(item => (
                      <Option key={item.value} value={item.value}>
                        {item.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="物品类型" name="itemTypes">
                  <Select
                    placeholder="请选择物品类型"
                    mode="multiple"
                    allowClear
                    style={{ width: '100%' }}
                  >
                    {configData.itemList?.map(item => (
                      <Option key={item.value} value={item.value}>
                        {item.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="创建时间" name="createTimeRange">
                  <RangePicker
                    style={{ width: '100%' }}
                    showTime
                    format="YYYY-MM-DD HH:mm:ss"
                    placeholder={['开始时间', '结束时间']}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col>
                <Button
                  type="primary"
                  onClick={handleSearch}
                  loading={loading}
                >
                  查询
                </Button>
              </Col>
            </Row>
          </Form>
          <Table
            className="sharkr-table"
            columns={columns}
            dataSource={dataSource}
            loading={loading}
            rowKey="id"
            scroll={{ x: 1200 }}
            pagination={{
              current: pagination.page || 1,
              pageSize: pagination.size || 10,
              total: pagination.total || 0,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
              onChange: handleTableChange,
              onShowSizeChange: handleTableChange
            }}
          />
        </div>
      </section>
    </>
  );
};
