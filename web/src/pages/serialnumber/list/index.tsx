import React, { useState, useEffect } from 'react';
import { Table, Form, Button, Select, DatePicker, Space, message } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { querySerialNumberList, querySkuAndEdition } from '../../../services/serialNumberService';
import {
  DhxyCodeInfoListVO,
  SerialNumberQueryParams,
  EditionItem,
  ItemTypeItem
} from '../../../interfaces/serialNumberInterface';

const { RangePicker } = DatePicker;
const { Option } = Select;

export const SerialNumberList: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<DhxyCodeInfoListVO[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [editionList, setEditionList] = useState<EditionItem[]>([]);
  const [itemList, setItemList] = useState<ItemTypeItem[]>([]);

  // 表格列定义
  const columns: ColumnsType<DhxyCodeInfoListVO> = [
    {
      title: '年份',
      dataIndex: 'actNumber',
      key: 'actNumber',
      width: 100,
    },
    {
      title: '版本类型',
      dataIndex: 'edition',
      key: 'edition',
      width: 120,
      render: (edition: number) => {
        const editionItem = editionList.find(item => item.id === edition);
        return editionItem ? editionItem.name : edition;
      },
    },
    {
      title: '商品类型',
      dataIndex: 'itemType',
      key: 'itemType',
      width: 120,
      render: (itemType: number) => {
        const itemTypeItem = itemList.find(item => item.id === itemType);
        return itemTypeItem ? itemTypeItem.name : itemType;
      },
    },
    {
      title: '预约码总数',
      dataIndex: 'resCodeSum',
      key: 'resCodeSum',
      width: 120,
    },
    {
      title: '已兑换数',
      dataIndex: 'resCodeUsedSum',
      key: 'resCodeUsedSum',
      width: 120,
    },
    {
      title: '激活码总数',
      dataIndex: 'actCodeSum',
      key: 'actCodeSum',
      width: 120,
    },
    {
      title: '激活码余数',
      dataIndex: 'resCodeNotUsedSum',
      key: 'resCodeNotUsedSum',
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
      render: (createTime: number) => {
        return createTime ? new Date(createTime).toLocaleString('zh-CN') : '-';
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Button type="link" onClick={() => handleViewDetail(record)}>
            查看详情
          </Button>
        </Space>
      ),
    },
  ];

  // 加载配置数据
  const loadConfigData = async () => {
    try {
      const response = await querySkuAndEdition();
      if (response.data?.editionList) {
        setEditionList(response.data.editionList);
      }
      if (response.data?.itemList) {
        setItemList(response.data.itemList);
      }
    } catch (error) {
      message.error('加载配置数据失败');
    }
  };

  // 加载列表数据
  const loadData = async (params?: Partial<SerialNumberQueryParams>) => {
    setLoading(true);
    try {
      const queryParams: SerialNumberQueryParams = {
        page: pagination.current,
        size: pagination.pageSize,
        ...params,
      };

      const response = await querySerialNumberList(queryParams);

      if (response.data?.result) {
        setDataSource(response.data.result);
      }

      if (response.data?.pagination) {
        setPagination(prev => ({
          ...prev,
          current: response.data?.pagination?.page || 1,
          total: response.data?.pagination?.total || 0,
        }));
      }
    } catch (error) {
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 搜索处理
  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params: Partial<SerialNumberQueryParams> = {
      page: 1,
    };

    if (values.dateRange && values.dateRange.length === 2) {
      params.createTimeStart = values.dateRange[0].format('YYYY-MM-DD');
      params.createTimeEnd = values.dateRange[1].format('YYYY-MM-DD');
    }

    if (values.editions && values.editions.length > 0) {
      params.editions = values.editions;
    }

    if (values.itemTypes && values.itemTypes.length > 0) {
      params.itemTypes = values.itemTypes;
    }

    setPagination(prev => ({ ...prev, current: 1 }));
    loadData(params);
  };

  // 重置处理
  const handleReset = () => {
    form.resetFields();
    setPagination(prev => ({ ...prev, current: 1 }));
    loadData();
  };

  // 编辑处理
  const handleEdit = (record: DhxyCodeInfoListVO) => {
    message.info(`编辑记录: ${record.id}`);
  };

  // 查看详情处理
  const handleViewDetail = (record: DhxyCodeInfoListVO) => {
    message.info(`查看详情: ${record.id}`);
  };

  // 表格分页变化处理
  const handleTableChange = (page: number, pageSize?: number) => {
    const newPagination = {
      ...pagination,
      current: page,
      pageSize: pageSize || pagination.pageSize,
    };
    setPagination(newPagination);
    
    const values = form.getFieldsValue();
    const params: Partial<SerialNumberQueryParams> = {
      page,
      size: pageSize || pagination.pageSize,
    };

    if (values.dateRange && values.dateRange.length === 2) {
      params.createTimeStart = values.dateRange[0].format('YYYY-MM-DD');
      params.createTimeEnd = values.dateRange[1].format('YYYY-MM-DD');
    }

    if (values.editions && values.editions.length > 0) {
      params.editions = values.editions;
    }

    if (values.itemTypes && values.itemTypes.length > 0) {
      params.itemTypes = values.itemTypes;
    }

    loadData(params);
  };

  useEffect(() => {
    loadConfigData();
    loadData();
  }, []);

  return (
    <div style={{ padding: '24px' }}>
      <h2>激活序列号</h2>
      
      {/* 搜索表单 */}
      <Form
        form={form}
        layout="inline"
        style={{ marginBottom: '16px' }}
        onFinish={handleSearch}
      >
        <Form.Item name="dateRange" label="创建时间">
          <RangePicker />
        </Form.Item>
        
        <Form.Item name="editions" label="版本类型">
          <Select
            mode="multiple"
            placeholder="请选择版本类型"
            style={{ width: 200 }}
            allowClear
          >
            {editionList.map(edition => (
              <Option key={edition.id} value={edition.id}>
                {edition.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item name="itemTypes" label="商品类型">
          <Select
            mode="multiple"
            placeholder="请选择商品类型"
            style={{ width: 200 }}
            allowClear
          >
            {itemList.map(item => (
              <Option key={item.id} value={item.id}>
                {item.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit">
              查询
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>

      {/* 操作按钮 */}
      <div style={{ marginBottom: '16px' }}>
        <Button type="primary">
          新建
        </Button>
      </div>

      {/* 数据表格 */}
      <Table
        columns={columns}
        dataSource={dataSource}
        loading={loading}
        rowKey="id"
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
          onChange: handleTableChange,
          onShowSizeChange: handleTableChange,
        }}
        scroll={{ x: 1200 }}
      />
    </div>
  );
};
