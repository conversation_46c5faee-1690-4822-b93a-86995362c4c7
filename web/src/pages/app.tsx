import React from 'react';
import { HashRouter as Router } from 'react-router-dom';
import {
  SharkRMenuProps
} from '@sharkr/components';
import { Layout } from './layouts';
import './app.scss';
import { AppRoute } from './appRoute';

export const App: React.FC = () => {
  const siderMenu: SharkRMenuProps = {
    data: [
      {
        name: '游戏序列号管理',
        icon: 'sharkr-xitongguanli',
        authCondition: 100,
        children: [
          {
            name: '激活序列号',
            icon: 'item',
            authCondition: 1001,
            link: {
              href: '#/serialnumber/list'
            }
          }
        ]
      }
    ]
  };

  return (
    <Router>
      <Layout siderMenu={siderMenu}>
        <AppRoute />
      </Layout>
    </Router>
  );
};
