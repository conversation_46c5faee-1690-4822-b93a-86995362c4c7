import React from 'react';
import { Route, Redirect, Switch } from 'react-router-dom';
import { CorePage404, CorePage500 } from '@sharkr/components';
import { SerialNumberList } from './serialnumber';

export const AppRoute: React.FC = () => (
  <Switch>
    <Route
      key="/serialnumber/list"
      path="/serialnumber/list"
      render={() => <SerialNumberList />}
    />
    <Redirect exact from="/" to="/serialnumber/list" />
    <Route component={CorePage500} key="/500" path="/500" />
    <Route component={CorePage404} />
  </Switch>
);
